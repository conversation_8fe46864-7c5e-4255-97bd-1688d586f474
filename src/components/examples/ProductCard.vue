<template>
  <Card
    :hoverable="true"
    variant="shadow"
    class="product-card group cursor-pointer overflow-hidden"
    @click="handleClick"
  >
    <!-- Product Image -->
    <div class="relative overflow-hidden">
      <img
        :src="product.image"
        :alt="product.name"
        class="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
        @error="handleImageError"
      />
      
      <!-- Overlay Actions -->
      <div class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-2">
        <Button
          variant="primary"
          size="sm"
          :icon="CartOutline"
          @click.stop="handleAddToCart"
        >
          Add to Cart
        </Button>
        <Button
          variant="ghost"
          size="sm"
          :icon="EyeOutline"
          class="text-white hover:text-white"
          @click.stop="handleQuickView"
        >
          Quick View
        </Button>
      </div>

      <!-- Favorite Button -->
      <button
        :class="favoriteButtonClasses"
        @click.stop="handleToggleFavorite"
      >
        <n-icon>
          <component :is="product.isFavorite ? Heart : HeartOutline" />
        </n-icon>
      </button>

      <!-- Badges -->
      <div class="absolute top-2 left-2 space-y-1">
        <Badge v-if="!product.inStock" variant="danger" size="sm">
          Out of Stock
        </Badge>
        <Badge v-if="product.originalPrice && product.originalPrice > product.price" variant="primary" size="sm">
          {{ discountPercentage }}% OFF
        </Badge>
      </div>
    </div>

    <!-- Product Info -->
    <div class="p-4">
      <!-- Category -->
      <Badge :variant="getCategoryVariant(product.category)" size="xs" class="mb-2">
        {{ product.category }}
      </Badge>

      <!-- Product Name -->
      <h3 :class="nameClasses">{{ product.name }}</h3>

      <!-- Rating -->
      <div class="flex items-center space-x-2 mb-2">
        <n-rate
          :value="product.rating"
          readonly
          size="small"
          color="#00C897"
        />
        <span :class="reviewsClasses">({{ product.reviews }})</span>
      </div>

      <!-- Price -->
      <div class="flex items-center space-x-2 mb-3">
        <span :class="priceClasses">${{ product.price }}</span>
        <span
          v-if="product.originalPrice && product.originalPrice > product.price"
          :class="originalPriceClasses"
        >
          ${{ product.originalPrice }}
        </span>
      </div>

      <!-- Brand -->
      <p :class="brandClasses">{{ product.brand }}</p>

      <!-- Quick Actions -->
      <div class="flex items-center justify-between mt-4">
        <Button
          variant="outline"
          size="sm"
          :disabled="!product.inStock"
          @click.stop="handleAddToCart"
        >
          <n-icon class="mr-1">
            <CartOutline />
          </n-icon>
          {{ product.inStock ? 'Add to Cart' : 'Out of Stock' }}
        </Button>

        <div class="flex items-center space-x-1">
          <button
            :class="actionButtonClasses"
            @click.stop="handleShare"
          >
            <n-icon>
              <ShareSocialOutline />
            </n-icon>
          </button>
          <button
            :class="actionButtonClasses"
            @click.stop="handleCompare"
          >
            <n-icon>
              <GitCompareOutline />
            </n-icon>
          </button>
        </div>
      </div>
    </div>
  </Card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NIcon, NRate } from 'naive-ui'
import {
  CartOutline,
  EyeOutline,
  Heart,
  HeartOutline,
  ShareSocialOutline,
  GitCompareOutline
} from '@vicons/ionicons5'
import { Card, Button, Badge } from '@/components/ui'
import { useThemeClasses } from '@/composables/useTheme'

interface Product {
  id: string
  name: string
  price: number
  originalPrice?: number
  image: string
  category: string
  brand: string
  rating: number
  reviews: number
  inStock: boolean
  isFavorite: boolean
  description: string
}

interface Props {
  product: Product
}

const props = defineProps<Props>()

const emit = defineEmits<{
  click: [product: Product]
  addToCart: [product: Product]
  toggleFavorite: [product: Product]
  quickView: [product: Product]
  share: [product: Product]
  compare: [product: Product]
}>()

const { getTextClass } = useThemeClasses()

// Computed
const nameClasses = computed(() => [
  getTextClass('primary'),
  'font-semibold text-lg mb-2 line-clamp-2'
].join(' '))

const priceClasses = computed(() => [
  'text-light-accent-sport dark:text-dark-accent-sport',
  'font-bold text-xl'
].join(' '))

const originalPriceClasses = computed(() => [
  getTextClass('muted'),
  'text-sm line-through'
].join(' '))

const brandClasses = computed(() => [
  getTextClass('secondary'),
  'text-sm'
].join(' '))

const reviewsClasses = computed(() => [
  getTextClass('muted'),
  'text-xs'
].join(' '))

const favoriteButtonClasses = computed(() => {
  const baseClass = 'absolute top-2 right-2 p-2 rounded-full transition-all duration-200'
  const favoriteClass = props.product.isFavorite
    ? 'bg-light-accent-danger dark:bg-dark-accent-danger text-white'
    : 'bg-white/80 dark:bg-black/80 text-light-text-muted dark:text-dark-text-muted hover:text-light-accent-danger dark:hover:text-dark-accent-danger'
  
  return `${baseClass} ${favoriteClass}`
})

const actionButtonClasses = computed(() => [
  getTextClass('muted'),
  'p-1 rounded-md transition-colors duration-200',
  'hover:bg-light-bg-secondary dark:hover:bg-dark-bg-secondary',
  'hover:text-light-text-primary dark:hover:text-dark-text-primary'
].join(' '))

const discountPercentage = computed(() => {
  if (!props.product.originalPrice) return 0
  return Math.round(((props.product.originalPrice - props.product.price) / props.product.originalPrice) * 100)
})

// Methods
const getCategoryVariant = (category: string) => {
  const variants: Record<string, any> = {
    'Football': 'success',
    'Basketball': 'warning',
    'Tennis': 'info',
    'Running': 'primary',
    'Gym': 'secondary'
  }
  return variants[category] || 'secondary'
}

const handleClick = () => {
  emit('click', props.product)
}

const handleAddToCart = () => {
  emit('addToCart', props.product)
}

const handleToggleFavorite = () => {
  emit('toggleFavorite', props.product)
}

const handleQuickView = () => {
  emit('quickView', props.product)
}

const handleShare = () => {
  emit('share', props.product)
}

const handleCompare = () => {
  emit('compare', props.product)
}

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  // Use a solid color background with the first letter as fallback
  target.src = `data:image/svg+xml;base64,${btoa(`
    <svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
      <rect width="300" height="200" fill="#00C897"/>
      <text x="150" y="110" font-family="Arial, sans-serif" font-size="48" font-weight="bold" text-anchor="middle" fill="white">
        ${props.product.name.charAt(0)}
      </text>
    </svg>
  `)}`
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
